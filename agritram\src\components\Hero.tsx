import { ArrowRight, Download, Play } from "lucide-react";
import { useState, useEffect } from "react";

const Hero = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <section
      id="hero"
      className="relative flex h-screen items-center justify-center overflow-hidden"
    >
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage:
            "url(https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-4.0.3)",
          backgroundSize: "cover",
          backgroundPosition: "center",
          transform: `translateY(${scrollY * 0.5}px)`,
        }}
      >
        {/* Floating animation elements */}
        <div className="absolute top-20 left-10 w-4 h-4 bg-green-400 rounded-full animate-float opacity-60"></div>
        <div className="absolute bottom-32 right-20 w-6 h-6 bg-emerald-300 rounded-full animate-float opacity-50" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/3 right-10 w-3 h-3 bg-green-300 rounded-full animate-float opacity-70" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/4 w-5 h-5 bg-lime-400 rounded-full animate-float opacity-40" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-green-500 rounded-full animate-float opacity-60" style={{ animationDelay: '1.5s' }}></div>

        <div className="hero__overlay absolute inset-0"></div>
      </div>

      {/* Hero Content */}
      <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-black leading-tight">
                Empowering Agriculture with{' '}
                <span className="text-[#2D4D31] relative">
                  Stable Finance
                  <div className="absolute -bottom-2 left-0 w-full h-1 bg-[#2D4D31]/20 rounded-full"></div>
                </span>
              </h1>
              <p className="text-xl text-gray-600 max-w-xl">
                Bringing stability, transparency, and growth to every crop and trade.
              </p>
            </div>

            {/* Primary CTAs */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="flex items-center justify-center px-8 py-4 bg-[#2D4D31] text-white rounded-lg hover:bg-[#2D4D31]/90 transition-all duration-300 font-medium border-2 border-[#2D4D31] border-dotted border-opacity-70 group">
                <Download className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                View Whitepaper
              </button>
              <button 
                // onClick={() => setShowVideo(true)}
                className="flex items-center justify-center px-8 py-4 bg-white text-[#2D4D31] rounded-lg hover:bg-gray-50 transition-all duration-300 font-medium border-2 border-[#2D4D31] group"
              >
                <Play className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Watch How It Works
              </button>
            </div>

            {/* Login Portals */}
            <div className="pt-8">
              <p className="text-sm text-gray-500 mb-4">Access your portal:</p>
              <div className="flex flex-wrap gap-3">
                <button className="px-6 py-3 bg-white text-[#2D4D31] rounded-lg hover:bg-gray-50 transition-colors font-medium border border-[#2D4D31]/20 flex items-center group">
                  Farmer Portal
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </button>
                <button className="px-6 py-3 bg-white text-[#2D4D31] rounded-lg hover:bg-gray-50 transition-colors font-medium border border-[#2D4D31]/20 flex items-center group">
                  Trader Portal
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </button>
                <button className="px-6 py-3 bg-white text-[#2D4D31] rounded-lg hover:bg-gray-50 transition-colors font-medium border border-[#2D4D31]/20 flex items-center group">
                  Manufacturer Portal
                  <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            <div className="relative bg-white rounded-2xl shadow-2xl p-8 border border-[#2D4D31]/10">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Agricultural Dashboard</h3>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span className="text-sm text-gray-500">Live</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-[#FFF5EA] p-4 rounded-lg">
                    <p className="text-sm text-gray-600">Total Volume</p>
                    <p className="text-2xl font-bold text-[#2D4D31]">$2.1M</p>
                  </div>
                  <div className="bg-[#FFF5EA] p-4 rounded-lg">
                    <p className="text-sm text-gray-600">Active Farmers</p>
                    <p className="text-2xl font-bold text-[#2D4D31]">1,247</p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">Corn Harvest Payment</span>
                    <span className="text-sm text-green-600 font-semibold">+$12,450</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">Wheat Trade Settlement</span>
                    <span className="text-sm text-green-600 font-semibold">+$8,230</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm font-medium">Soybean Contract</span>
                    <span className="text-sm text-blue-600 font-semibold">Pending</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      
    </section>
  );
};

export default Hero;